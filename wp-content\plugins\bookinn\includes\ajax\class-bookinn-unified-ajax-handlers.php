<?php
/**
 * BookInn Unified AJAX Handlers
 *
 * Consolidated AJAX handlers for the BookInn plugin
 * Handles all AJAX requests from management dashboard and frontend
 *
 * MIGRATION NOTES:
 * - Migrated functionality from class-bookinn-ajax-booking-handlers.php
 * - All booking-related AJAX handlers are now unified in this file
 * - Original file moved to old-files for reference
 *
 * @package BookInn
 * @version 1.0.9
 */

if (!defined('ABSPATH')) {
    exit;
}

class BookInn_Unified_Ajax_Handlers {
    
    public function __construct() {
        $this->init_hooks();
    }
    
    /**
     * Initialize WordPress AJAX hooks
     */
    private function init_hooks() {
        // Booking management
        add_action('wp_ajax_bookinn_get_bookings', array($this, 'get_bookings'));
        add_action('wp_ajax_bookinn_get_booking', array($this, 'get_booking'));
        add_action('wp_ajax_bookinn_get_booking_details', array($this, 'get_booking_details'));
        add_action('wp_ajax_bookinn_save_booking', array($this, 'save_booking'));
        add_action('wp_ajax_bookinn_create_booking', array($this, 'create_booking'));
        // Edit booking functionality removed
        add_action('wp_ajax_bookinn_delete_booking', array($this, 'delete_booking'));
        add_action('wp_ajax_bookinn_update_booking_dates', array($this, 'update_booking_dates'));
        
        // Room management
        add_action('wp_ajax_bookinn_get_rooms', array($this, 'get_rooms'));
        add_action('wp_ajax_bookinn_get_room', array($this, 'get_room'));
        add_action('wp_ajax_bookinn_get_available_rooms', array($this, 'get_available_rooms'));
        add_action('wp_ajax_bookinn_save_room', array($this, 'save_room')); // Unified save handler
        add_action('wp_ajax_bookinn_create_room', array($this, 'create_room'));
        add_action('wp_ajax_bookinn_update_room', array($this, 'update_room'));
        add_action('wp_ajax_bookinn_delete_room', array($this, 'delete_room'));
        
        // Room type management
        add_action('wp_ajax_bookinn_get_room_types', array($this, 'get_room_types'));
        add_action('wp_ajax_bookinn_get_room_type', array($this, 'get_room_type'));
        add_action('wp_ajax_bookinn_save_room_type', array($this, 'save_room_type')); // Unified save handler
        add_action('wp_ajax_bookinn_create_room_type', array($this, 'create_room_type'));
        add_action('wp_ajax_bookinn_update_room_type', array($this, 'update_room_type'));
        add_action('wp_ajax_bookinn_delete_room_type', array($this, 'delete_room_type'));
        
        // Calendar and analytics
        add_action('wp_ajax_bookinn_get_calendar_events', array($this, 'get_calendar_events'));
        add_action('wp_ajax_bookinn_get_analytics_data', array($this, 'get_analytics_data'));
        add_action('wp_ajax_bookinn_refresh_widget_data', array($this, 'refresh_widget_data'));
        
        // Frontend public endpoints (for booking widgets)
        add_action('wp_ajax_nopriv_bookinn_get_available_rooms', array($this, 'get_available_rooms'));
        add_action('wp_ajax_nopriv_bookinn_save_booking', array($this, 'save_booking'));
    }
    
    /**
     * Multi-nonce verification system
     */
    private function verify_nonce() {
        $nonce = $_POST['nonce'] ?? '';
        
        error_log("BookInn AJAX: Nonce verification attempt with nonce: " . substr($nonce, 0, 10) . '...');
        
        if (empty($nonce)) {
            error_log("BookInn AJAX: No nonce provided");
            wp_send_json_error('Security verification failed - no nonce provided');
            return false;
        }
        
        // Try multiple nonce actions for compatibility
        $nonce_actions = array(
            'bookinn_management_nonce',
            'bookinn_dashboard_nonce', 
            'bookinn_frontend_nonce',
            'bookinn_nonce',
            'bookinn_ajax'  // Add this for ajax_nonce from JS
        );
        
        $verified = false;
        foreach ($nonce_actions as $action) {
            if (wp_verify_nonce($nonce, $action)) {
                error_log("BookInn AJAX: Nonce verified successfully with action: $action");
                $verified = true;
                break;
            }
        }
        
        if (!$verified) {
            error_log("BookInn AJAX: Nonce verification failed for all actions. Tested: " . implode(', ', $nonce_actions));
            wp_send_json_error('Security verification failed');
            return false;
        }
        
        return true;
    }
    
    /**
     * Get all bookings with pagination and filtering
     */
    public function get_bookings() {
        if (!$this->verify_nonce()) return;

        $page = intval($_POST['page'] ?? 1);
        $per_page = intval($_POST['per_page'] ?? 20);
        $search = sanitize_text_field($_POST['search'] ?? '');
        $status = sanitize_text_field($_POST['status'] ?? '');

        global $wpdb;

        try {
            $bookings_table = $wpdb->prefix . 'bookinn_bookings';
            $rooms_table = $wpdb->prefix . 'bookinn_rooms';
            $guests_table = $wpdb->prefix . 'bookinn_guests';
            
            $where_conditions = array('1=1');
            $where_values = array();

            if (!empty($search)) {
                $where_conditions[] = '(CONCAT(g.first_name, " ", g.last_name) LIKE %s OR g.email LIKE %s OR b.booking_reference LIKE %s)';
                $where_values[] = '%' . $search . '%';
                $where_values[] = '%' . $search . '%';
                $where_values[] = '%' . $search . '%';
            }

            if (!empty($status)) {
                $where_conditions[] = 'b.status = %s';
                $where_values[] = $status;
            }

            $where_clause = implode(' AND ', $where_conditions);
            $offset = ($page - 1) * $per_page;

            $query = "
                SELECT b.*, 
                       r.room_number, 
                       rt.name as room_type_name,
                       g.first_name as guest_first_name,
                       g.last_name as guest_last_name,
                       g.email as guest_email,
                       g.phone as guest_phone,
                       CONCAT(g.first_name, ' ', g.last_name) as guest_name
                FROM {$bookings_table} b
                LEFT JOIN {$rooms_table} r ON b.room_id = r.id
                LEFT JOIN {$wpdb->prefix}bookinn_room_types rt ON r.room_type_id = rt.id
                LEFT JOIN {$guests_table} g ON b.guest_id = g.id
                WHERE {$where_clause}
                ORDER BY b.created_at DESC
                LIMIT %d OFFSET %d
            ";

            $query_values = array_merge($where_values, array($per_page, $offset));
            $bookings = $wpdb->get_results($wpdb->prepare($query, $query_values));

            // Get total count
            $count_query = "SELECT COUNT(*) FROM {$bookings_table} b LEFT JOIN {$guests_table} g ON b.guest_id = g.id WHERE {$where_clause}";
            $total = $wpdb->get_var($wpdb->prepare($count_query, $where_values));

            wp_send_json_success(array(
                'bookings' => $bookings,
                'total' => intval($total),
                'page' => $page,
                'per_page' => $per_page
            ));

        } catch (Exception $e) {
            error_log('BookInn Get Bookings Error: ' . $e->getMessage());
            wp_send_json_error('Failed to load bookings');
        }
    }

    /**
     * Get single booking details
     */
    public function get_booking() {
        if (!$this->verify_nonce()) return;

        $booking_id = intval($_POST['booking_id'] ?? 0);

        if (!$booking_id) {
            error_log('BookInn Get Booking Error: No booking ID provided');
            wp_send_json_error('Booking ID is required');
            return;
        }

        global $wpdb;

        try {
            $bookings_table = $wpdb->prefix . 'bookinn_bookings';
            $rooms_table = $wpdb->prefix . 'bookinn_rooms';
            $guests_table = $wpdb->prefix . 'bookinn_guests';

            error_log("BookInn Get Booking: Loading booking ID {$booking_id}");

            // First try modern table structure
            $booking = $wpdb->get_row($wpdb->prepare("
                SELECT b.*, 
                       r.room_number, 
                       rt.name as room_type_name,
                       g.first_name as guest_first_name,
                       g.last_name as guest_last_name,
                       g.email as guest_email,
                       g.phone as guest_phone,
                       g.address as guest_address,
                       g.city as guest_city,
                       g.country as guest_country,
                       CONCAT(g.first_name, ' ', g.last_name) as guest_name
                FROM {$bookings_table} b
                LEFT JOIN {$rooms_table} r ON b.room_id = r.id
                LEFT JOIN {$wpdb->prefix}bookinn_room_types rt ON r.room_type_id = rt.id
                LEFT JOIN {$guests_table} g ON b.guest_id = g.id
                WHERE b.id = %d
            ", $booking_id));

            if ($booking) {
                error_log("BookInn Get Booking: Found in modern table");
                // Debug room data specifically
                error_log("BookInn Get Booking: Room data - room_id: {$booking->room_id}, room_number: " . ($booking->room_number ?: 'NULL') . ", room_type_name: " . ($booking->room_type_name ?: 'NULL'));

                // Debug guest data
                error_log("BookInn Get Booking: Guest data - guest_id: " . ($booking->guest_id ?? 'NULL') . ", first_name: " . ($booking->guest_first_name ?? 'NULL') . ", last_name: " . ($booking->guest_last_name ?? 'NULL') . ", email: " . ($booking->guest_email ?? 'NULL') . ", phone: " . ($booking->guest_phone ?? 'NULL') . ", address: " . ($booking->guest_address ?? 'NULL') . ", city: " . ($booking->guest_city ?? 'NULL') . ", country: " . ($booking->guest_country ?? 'NULL'));

                // If room_number is null but room_id exists, try to get room info separately
                if ($booking->room_id && !$booking->room_number) {
                    error_log("BookInn Get Booking: Room number missing, querying room table separately");
                    $room_info = $wpdb->get_row($wpdb->prepare("
                        SELECT r.room_number, rt.name as room_type_name 
                        FROM {$rooms_table} r 
                        LEFT JOIN {$wpdb->prefix}bookinn_room_types rt ON r.room_type_id = rt.id 
                        WHERE r.id = %d
                    ", $booking->room_id));
                    
                    if ($room_info) {
                        $booking->room_number = $room_info->room_number;
                        $booking->room_type_name = $room_info->room_type_name;
                        error_log("BookInn Get Booking: Found room info - room_number: {$room_info->room_number}, room_type_name: {$room_info->room_type_name}");
                    } else {
                        error_log("BookInn Get Booking: Room {$booking->room_id} not found in rooms table");
                    }
                }
            };

            // If not found, try legacy booking_reservations table
            if (!$booking) {
                error_log("BookInn Get Booking: Not found in modern table, trying legacy table");
                $legacy_table = $wpdb->prefix . 'booking_reservations';
                $legacy_rooms_table = $wpdb->prefix . 'booking_rooms';
                
                $booking = $wpdb->get_row($wpdb->prepare("
                    SELECT r.*, 
                           br.room_number,
                           br.room_type,
                           r.guest_name,
                           r.guest_email,
                           r.guest_phone,
                           r.guest_address,
                           r.check_in as check_in_date,
                           r.check_out as check_out_date,
                           r.total_price as total_amount,
                           r.notes as special_requests
                    FROM {$legacy_table} r
                    LEFT JOIN {$legacy_rooms_table} br ON r.room_id = br.id
                    WHERE r.id = %d
                ", $booking_id));
                
                if ($booking) {
                    error_log("BookInn Get Booking: Found in legacy table");
                }
            } else {
                error_log("BookInn Get Booking: Found in modern table");
            }

            if (!$booking) {
                error_log("BookInn Get Booking Error: Booking {$booking_id} not found in any table");
                wp_send_json_error('Booking not found');
                return;
            }

            error_log("BookInn Get Booking Success: " . json_encode($booking));
            wp_send_json_success($booking);

        } catch (Exception $e) {
            error_log('BookInn Get Booking Error: ' . $e->getMessage());
            wp_send_json_error('Failed to load booking details');
        }
    }

    /**
     * Alias for get_booking (for backward compatibility)
     */
    public function get_booking_details() {
        $this->get_booking();
    }

    /**
     * Save/update booking
     */
    public function save_booking() {
        if (!$this->verify_nonce()) return;

        try {
            $booking_id = intval($_POST['booking_id'] ?? 0);
            $guest_name = sanitize_text_field($_POST['guest_name'] ?? '');
            $guest_email = sanitize_email($_POST['guest_email'] ?? '');
            $guest_phone = sanitize_text_field($_POST['guest_phone'] ?? '');
            $room_id = intval($_POST['room_id'] ?? 0);
            $checkin_date = sanitize_text_field($_POST['checkin_date'] ?? $_POST['check_in_date'] ?? '');
            $checkout_date = sanitize_text_field($_POST['checkout_date'] ?? $_POST['check_out_date'] ?? '');
            $adults = intval($_POST['adults'] ?? 1);
            $children = intval($_POST['children'] ?? 0);
            $total_amount = floatval($_POST['total_amount'] ?? 0);
            $status = sanitize_text_field($_POST['status'] ?? 'pending');
            $special_requests = sanitize_textarea_field($_POST['special_requests'] ?? '');
            $internal_notes = sanitize_textarea_field($_POST['internal_notes'] ?? '');

            // Validation
            if (empty($guest_name) || empty($guest_email) || empty($checkin_date) || empty($checkout_date)) {
                wp_send_json_error('Required fields are missing');
                return;
            }

            if (!is_email($guest_email)) {
                wp_send_json_error('Invalid email address');
                return;
            }

            global $wpdb;
            $bookings_table = $wpdb->prefix . 'bookinn_bookings';

            $booking_data = array(
                'guest_name' => $guest_name,
                'guest_email' => $guest_email,
                'guest_phone' => $guest_phone,
                'room_id' => $room_id,
                'check_in_date' => $checkin_date,
                'check_out_date' => $checkout_date,
                'adults' => $adults,
                'children' => $children,
                'total_amount' => $total_amount,
                'status' => $status,
                'special_requests' => $special_requests,
                'internal_notes' => $internal_notes,
                'updated_at' => current_time('mysql')
            );

            if ($booking_id > 0) {
                // Update existing booking
                $result = $wpdb->update(
                    $bookings_table,
                    $booking_data,
                    array('id' => $booking_id),
                    array('%s', '%s', '%s', '%d', '%s', '%s', '%d', '%d', '%f', '%s', '%s', '%s', '%s'),
                    array('%d')
                );

                if ($result !== false) {
                    wp_send_json_success(array(
                        'message' => 'Booking updated successfully',
                        'booking_id' => $booking_id
                    ));
                } else {
                    wp_send_json_error('Failed to update booking');
                }
            } else {
                // Create new booking
                $booking_data['created_at'] = current_time('mysql');
                
                $result = $wpdb->insert(
                    $bookings_table,
                    $booking_data,
                    array('%s', '%s', '%s', '%d', '%s', '%s', '%d', '%d', '%f', '%s', '%s', '%s', '%s', '%s')
                );

                if ($result) {
                    $new_booking_id = $wpdb->insert_id;
                    wp_send_json_success(array(
                        'message' => 'Booking created successfully',
                        'booking_id' => $new_booking_id
                    ));
                } else {
                    wp_send_json_error('Failed to create booking');
                }
            }

        } catch (Exception $e) {
            error_log('BookInn Save Booking Error: ' . $e->getMessage());
            wp_send_json_error('Failed to save booking: ' . $e->getMessage());
        }
    }

    /**
     * Alias for save_booking (create new)
     */
    public function create_booking() {
        // Remove booking_id to force creation
        unset($_POST['booking_id']);
        $this->save_booking();
    }

   

    /**
     * Delete booking
     */
    public function delete_booking() {
        if (!$this->verify_nonce()) return;

        $booking_id = intval($_POST['booking_id'] ?? 0);

        if (!$booking_id) {
            wp_send_json_error('Booking ID is required');
            return;
        }

        global $wpdb;

        try {
            $result = $wpdb->delete(
                $wpdb->prefix . 'bookinn_bookings',
                array('id' => $booking_id),
                array('%d')
            );

            if ($result) {
                wp_send_json_success('Booking deleted successfully');
            } else {
                wp_send_json_error('Failed to delete booking');
            }

        } catch (Exception $e) {
            error_log('BookInn Delete Booking Error: ' . $e->getMessage());
            wp_send_json_error('Failed to delete booking');
        }
    }

    /**
     * Update booking dates (for calendar drag & drop)
     */
    public function update_booking_dates() {
        if (!$this->verify_nonce()) return;

        try {
            $booking_id = intval($_POST['booking_id'] ?? 0);
            $start_date = sanitize_text_field($_POST['start_date'] ?? '');
            $end_date = sanitize_text_field($_POST['end_date'] ?? '');

            if (!$booking_id || !$start_date || !$end_date) {
                wp_send_json_error('Missing required parameters');
                return;
            }

            global $wpdb;

            $result = $wpdb->update(
                $wpdb->prefix . 'bookinn_bookings',
                array(
                    'check_in_date' => $start_date,
                    'check_out_date' => $end_date,
                    'updated_at' => current_time('mysql')
                ),
                array('id' => $booking_id),
                array('%s', '%s', '%s'),
                array('%d')
            );

            if ($result !== false) {
                wp_send_json_success('Booking dates updated successfully');
            } else {
                wp_send_json_error('Failed to update booking dates');
            }

        } catch (Exception $e) {
            error_log('BookInn Update Booking Dates Error: ' . $e->getMessage());
            wp_send_json_error('Failed to update booking dates');
        }
    }

    /**
     * Get available rooms for given criteria
     */
    public function get_available_rooms() {
        if (!$this->verify_nonce()) return;

        $checkin_date = sanitize_text_field($_POST['checkin_date'] ?? $_POST['check_in_date'] ?? '');
        $checkout_date = sanitize_text_field($_POST['checkout_date'] ?? $_POST['check_out_date'] ?? '');
        $adults = intval($_POST['adults'] ?? 1);
        $children = intval($_POST['children'] ?? 0);

        if (!$checkin_date || !$checkout_date) {
            wp_send_json_error('Check-in and check-out dates are required');
            return;
        }

        global $wpdb;

        try {
            $rooms_table = $wpdb->prefix . 'bookinn_rooms';
            $bookings_table = $wpdb->prefix . 'bookinn_bookings';
            $room_types_table = $wpdb->prefix . 'bookinn_room_types';

            $total_guests = $adults + $children;

            $rooms = $wpdb->get_results($wpdb->prepare("
                SELECT r.*, rt.name as room_type_name, rt.base_price
                FROM {$rooms_table} r
                LEFT JOIN {$room_types_table} rt ON r.room_type_id = rt.id
                WHERE r.status = 'available'
                AND rt.max_guests >= %d
                AND r.id NOT IN (
                    SELECT room_id FROM {$bookings_table}
                    WHERE status NOT IN ('cancelled', 'checked_out')
                    AND (
                        (check_in_date <= %s AND check_out_date > %s)
                        OR (check_in_date < %s AND check_out_date >= %s)
                        OR (check_in_date >= %s AND check_out_date <= %s)
                    )
                )
                ORDER BY rt.base_price ASC
            ", 
                $total_guests,
                $checkin_date, $checkin_date,
                $checkout_date, $checkout_date,
                $checkin_date, $checkout_date
            ));

            // Format room data to match JavaScript expectations
            $formatted_rooms = array();
            foreach ($rooms as $room) {
                $formatted_rooms[] = array(
                    'id' => isset($room->id) ? $room->id : '',
                    'room_number' => isset($room->room_number) ? $room->room_number : '',
                    'room_name' => isset($room->room_name) && $room->room_name ? $room->room_name : ('Room ' . (isset($room->room_number) ? $room->room_number : '')),
                    'room_type_name' => isset($room->room_type_name) && $room->room_type_name ? $room->room_type_name : 'Standard',
                    'name' => isset($room->room_type_name) && $room->room_type_name ? $room->room_type_name : 'Standard', // alias
                    'base_price' => isset($room->base_price) ? floatval($room->base_price) : 0,
                    'price' => isset($room->base_price) ? floatval($room->base_price) : 0, // alias
                    'description' => isset($room->description) ? $room->description : '',
                    'amenities' => (isset($room->amenities) && $room->amenities) ? json_decode($room->amenities, true) : array()
                );
            }

            wp_send_json_success($formatted_rooms);

        } catch (Exception $e) {
            error_log('BookInn Get Available Rooms Error: ' . $e->getMessage());
            wp_send_json_error('Failed to load available rooms: ' . $e->getMessage());
        }
    }

    /**
     * Get calendar events for FullCalendar with availability information
     */
    public function get_calendar_events() {
        if (!$this->verify_nonce()) return;

        try {
            $start = sanitize_text_field($_POST['start'] ?? date('Y-m-01'));
            $end = sanitize_text_field($_POST['end'] ?? date('Y-m-t'));
            $room_filter = intval($_POST['room_id'] ?? 0);
            $room_type_filter = intval($_POST['room_type_id'] ?? 0);
            $status_filter = sanitize_text_field($_POST['status'] ?? '');
            $show_availability = sanitize_text_field($_POST['show_availability'] ?? 'bookings');

            global $wpdb;

            $where_conditions = array();
            $params = array($start, $end);

            // Add room filter
            if ($room_filter > 0) {
                $where_conditions[] = 'b.room_id = %d';
                $params[] = $room_filter;
            }

            // Add room type filter
            if ($room_type_filter > 0) {
                $where_conditions[] = 'rt.id = %d';
                $params[] = $room_type_filter;
            }

            // Add status filter
            if (!empty($status_filter)) {
                $where_conditions[] = 'b.status = %s';
                $params[] = $status_filter;
            }

            $where_clause = '';
            if (!empty($where_conditions)) {
                $where_clause = 'AND ' . implode(' AND ', $where_conditions);
            }

            $events = array();
            
            // Get bookings with room information
            $query_params = array_merge(array($end, $start), array_slice($params, 2));
            $bookings = $wpdb->get_results($wpdb->prepare("
                SELECT b.*, 
                       r.room_number, 
                       rt.name as room_type_name,
                       CONCAT(g.first_name, ' ', g.last_name) as guest_full_name
                FROM {$wpdb->prefix}bookinn_bookings b
                LEFT JOIN {$wpdb->prefix}bookinn_rooms r ON b.room_id = r.id
                LEFT JOIN {$wpdb->prefix}bookinn_room_types rt ON r.room_type_id = rt.id
                LEFT JOIN {$wpdb->prefix}bookinn_guests g ON b.guest_id = g.id
                WHERE b.check_in_date <= %s AND b.check_out_date >= %s
                $where_clause
                ORDER BY b.check_in_date
            ", $query_params));

            // Process bookings into calendar events
            foreach ($bookings as $booking) {
                $color = $this->get_availability_color($booking->status);
                
                $events[] = array(
                    'id' => 'booking_' . $booking->id,
                    'title' => $this->format_event_title($booking),
                    'start' => $booking->check_in_date,
                    'end' => $booking->check_out_date,
                    'backgroundColor' => $color['bg'],
                    'borderColor' => $color['border'],
                    'textColor' => $color['text'],
                    'extendedProps' => array(
                        'booking_id' => $booking->id,
                        'guest_name' => $booking->guest_full_name ?: $booking->guest_name,
                        'room_number' => $booking->room_number,
                        'room_type' => $booking->room_type_name,
                        'status' => $booking->status,
                        'total_amount' => $booking->total_amount,
                        'adults' => $booking->adults,
                        'children' => $booking->children,
                        'type' => 'booking'
                    )
                );
            }

            // Add availability indicators if requested
            if ($show_availability === 'availability' || $show_availability === 'both') {
                $availability_events = $this->get_room_availability_events($start, $end, $room_filter, $room_type_filter);
                $events = array_merge($events, $availability_events);
            }

            wp_send_json_success($events);

        } catch (Exception $e) {
            error_log('BookInn Calendar Events Error: ' . $e->getMessage());
            wp_send_json_error('Error loading calendar events');
        }
    }

    /**
     * Get analytics data for dashboard
     */
    public function get_analytics_data() {
        if (!$this->verify_nonce()) return;

        global $wpdb;

        try {
            $data = array();

            // Basic stats
            $bookings_table = $wpdb->prefix . 'bookinn_bookings';
            
            $data['total_bookings'] = $wpdb->get_var("SELECT COUNT(*) FROM {$bookings_table}");
            $data['revenue_total'] = $wpdb->get_var("SELECT SUM(total_amount) FROM {$bookings_table} WHERE status != 'cancelled'");
            $data['pending_bookings'] = $wpdb->get_var("SELECT COUNT(*) FROM {$bookings_table} WHERE status = 'pending'");
            $data['confirmed_bookings'] = $wpdb->get_var("SELECT COUNT(*) FROM {$bookings_table} WHERE status = 'confirmed'");

            wp_send_json_success($data);

        } catch (Exception $e) {
            error_log('BookInn Analytics Error: ' . $e->getMessage());
            wp_send_json_error('Error loading analytics data');
        }
    }

    /**
     * Refresh widget data
     */
    public function refresh_widget_data() {
        if (!$this->verify_nonce()) return;

        try {
            // Clear any relevant caches
            if (function_exists('wp_cache_flush')) {
                wp_cache_flush();
            }

            wp_send_json_success(array(
                'message' => 'Data refreshed successfully'
            ));

        } catch (Exception $e) {
            error_log('BookInn Refresh Data Error: ' . $e->getMessage());
            wp_send_json_error('Error refreshing data');
        }
    }

    /**
     * Get room management data
     */
    public function get_rooms() {
        if (!$this->verify_nonce()) return;

        global $wpdb;

        try {
            $rooms = $wpdb->get_results("
                SELECT r.*, rt.name as room_type_name, rt.base_price
                FROM {$wpdb->prefix}bookinn_rooms r
                LEFT JOIN {$wpdb->prefix}bookinn_room_types rt ON r.room_type_id = rt.id
                ORDER BY r.room_number
            ");

            wp_send_json_success($rooms);

        } catch (Exception $e) {
            error_log('BookInn Get Rooms Error: ' . $e->getMessage());
            wp_send_json_error('Failed to load rooms');
        }
    }

    /**
     * Get single room details
     */
    public function get_room() {
        if (!$this->verify_nonce()) return;

        $room_id = intval($_POST['room_id'] ?? 0);

        if (!$room_id) {
            wp_send_json_error('Room ID is required');
            return;
        }

        global $wpdb;

        try {
            $room = $wpdb->get_row($wpdb->prepare("
                SELECT r.*, rt.name as room_type_name, rt.base_price
                FROM {$wpdb->prefix}bookinn_rooms r
                LEFT JOIN {$wpdb->prefix}bookinn_room_types rt ON r.room_type_id = rt.id
                WHERE r.id = %d
            ", $room_id));

            if (!$room) {
                wp_send_json_error('Room not found');
                return;
            }

            wp_send_json_success($room);

        } catch (Exception $e) {
            error_log('BookInn Get Room Error: ' . $e->getMessage());
            wp_send_json_error('Failed to load room details');
        }
    }

    /**
     * Get room types
     */
    public function get_room_types() {
        if (!$this->verify_nonce()) return;

        global $wpdb;

        try {
            $room_types = $wpdb->get_results("
                SELECT * FROM {$wpdb->prefix}bookinn_room_types
                ORDER BY name
            ");

            wp_send_json_success($room_types);

        } catch (Exception $e) {
            error_log('BookInn Get Room Types Error: ' . $e->getMessage());
            wp_send_json_error('Failed to load room types');
        }
    }

    /**
     * Get single room type details
     */
    public function get_room_type() {
        if (!$this->verify_nonce()) return;

        $room_type_id = intval($_POST['room_type_id'] ?? 0);

        if (!$room_type_id) {
            wp_send_json_error('Room type ID is required');
            return;
        }

        global $wpdb;

        try {
            $room_type = $wpdb->get_row($wpdb->prepare("
                SELECT * FROM {$wpdb->prefix}bookinn_room_types
                WHERE id = %d
            ", $room_type_id));

            if (!$room_type) {
                wp_send_json_error('Room type not found');
                return;
            }

            wp_send_json_success($room_type);

        } catch (Exception $e) {
            error_log('BookInn Get Room Type Error: ' . $e->getMessage());
            wp_send_json_error('Failed to load room type details');
        }
    }

    /**
     * Create new room
     */
    public function create_room() {
        if (!$this->verify_nonce()) return;

        try {
            $room_data = array(
                'hotel_id' => intval($_POST['hotel_id'] ?? 1), // Default to first hotel
                'room_type_id' => intval($_POST['room_type_id'] ?? 0),
                'room_number' => sanitize_text_field($_POST['room_number'] ?? ''),
                'name' => sanitize_text_field($_POST['name'] ?? ''),
                'floor' => intval($_POST['floor'] ?? 0),
                'status' => sanitize_text_field($_POST['status'] ?? 'available'),
                'is_active' => intval($_POST['is_active'] ?? 1),
                'created_at' => current_time('mysql')
            );

            if (empty($room_data['room_number'])) {
                wp_send_json_error('Room number is required');
                return;
            }
            if (empty($room_data['name'])) {
                wp_send_json_error('Room name is required');
                return;
            }
            if (empty($room_data['room_type_id'])) {
                wp_send_json_error('Room type is required');
                return;
            }

            global $wpdb;

            $result = $wpdb->insert(
                $wpdb->prefix . 'bookinn_rooms',
                $room_data,
                array('%d', '%d', '%s', '%s', '%d', '%s', '%d', '%s')
            );

            if ($result) {
                wp_send_json_success(array(
                    'message' => 'Room created successfully',
                    'room_id' => $wpdb->insert_id
                ));
            } else {
                wp_send_json_error('Failed to create room');
            }

        } catch (Exception $e) {
            error_log('BookInn Create Room Error: ' . $e->getMessage());
            wp_send_json_error('Failed to create room');
        }
    }

    /**
     * Update room
     */
    public function update_room() {
        if (!$this->verify_nonce()) return;

        try {
            $room_id = intval($_POST['room_id'] ?? 0);
            
            if (!$room_id) {
                wp_send_json_error('Room ID is required');
                return;
            }

            $room_type_id = isset($_POST['room_type_id']) ? intval($_POST['room_type_id']) : 0;
            $room_data = array(
                'hotel_id' => intval($_POST['hotel_id'] ?? 1),
                'room_type_id' => $room_type_id,
                'room_number' => sanitize_text_field($_POST['room_number'] ?? ''),
                'name' => sanitize_text_field($_POST['name'] ?? ''),
                'floor' => intval($_POST['floor'] ?? 0),
                'status' => sanitize_text_field($_POST['status'] ?? 'available'),
                'is_active' => intval($_POST['is_active'] ?? 1),
                'updated_at' => current_time('mysql')
            );

            // Validation
            if (empty($room_data['room_number'])) {
                wp_send_json_error('Room number is required');
                return;
            }

            if (empty($room_data['name'])) {
                wp_send_json_error('Room name is required');
                return;
            }

            if ($room_type_id <= 0) {
                wp_send_json_error('Room type is required and must be valid');
                return;
            }

            global $wpdb;

            $result = $wpdb->update(
                $wpdb->prefix . 'bookinn_rooms',
                $room_data,
                array('id' => $room_id),
                array('%d', '%d', '%s', '%s', '%d', '%s', '%d', '%s'),
                array('%d')
            );

            if ($result !== false) {
                wp_send_json_success('Room updated successfully');
            } else {
                wp_send_json_error('Failed to update room');
            }

        } catch (Exception $e) {
            error_log('BookInn Update Room Error: ' . $e->getMessage());
            wp_send_json_error('Failed to update room');
        }
    }

    /**
     * Delete room
     */
    public function delete_room() {
        if (!$this->verify_nonce()) return;

        $room_id = intval($_POST['room_id'] ?? 0);

        if (!$room_id) {
            wp_send_json_error('Room ID is required');
            return;
        }

        global $wpdb;

        try {
            // Check if room has active bookings
            $active_bookings = $wpdb->get_var($wpdb->prepare("
                SELECT COUNT(*) 
                FROM {$wpdb->prefix}bookinn_bookings 
                WHERE room_id = %d 
                AND status NOT IN ('cancelled', 'checked_out', 'no_show')
                AND check_out_date >= CURDATE()
            ", $room_id));

            if ($active_bookings > 0) {
                wp_send_json_error('Cannot delete room: it has active or future bookings. Please cancel or complete all bookings first.');
                return;
            }

            // Check if room exists
            $room_exists = $wpdb->get_var($wpdb->prepare("
                SELECT COUNT(*) 
                FROM {$wpdb->prefix}bookinn_rooms 
                WHERE id = %d
            ", $room_id));

            if (!$room_exists) {
                wp_send_json_error('Room not found');
                return;
            }

            // Delete the room
            $result = $wpdb->delete(
                $wpdb->prefix . 'bookinn_rooms',
                array('id' => $room_id),
                array('%d')
            );

            if ($result) {
                wp_send_json_success('Room deleted successfully');
            } else {
                wp_send_json_error('Failed to delete room');
            }

        } catch (Exception $e) {
            error_log('BookInn Delete Room Error: ' . $e->getMessage());
            wp_send_json_error('Failed to delete room: ' . $e->getMessage());
        }
    }

    /**
     * Create room type
     */
    public function create_room_type() {
        if (!$this->verify_nonce()) return;

        try {
            $room_type_data = array(
                'hotel_id' => intval($_POST['hotel_id'] ?? 1), // Default to first hotel
                'name' => sanitize_text_field($_POST['name'] ?? ''),
                'description' => sanitize_textarea_field($_POST['description'] ?? ''),
                'max_adults' => intval($_POST['max_adults'] ?? 2),
                'max_children' => intval($_POST['max_children'] ?? 0),
                'max_guests' => intval($_POST['max_guests'] ?? 2),
                'base_price' => floatval($_POST['base_price'] ?? 0),
                'amenities' => sanitize_textarea_field($_POST['amenities'] ?? ''),
                'images' => sanitize_textarea_field($_POST['images'] ?? ''),
                'is_active' => intval($_POST['is_active'] ?? 1),
                'created_at' => current_time('mysql')
            );

            if (empty($room_type_data['name'])) {
                wp_send_json_error('Room type name is required');
                return;
            }

            global $wpdb;

            $result = $wpdb->insert(
                $wpdb->prefix . 'bookinn_room_types',
                $room_type_data,
                array('%d', '%s', '%s', '%d', '%d', '%d', '%f', '%s', '%s', '%d', '%s')
            );

            if ($result) {
                wp_send_json_success(array(
                    'message' => 'Room type created successfully',
                    'room_type_id' => $wpdb->insert_id
                ));
            } else {
                wp_send_json_error('Failed to create room type');
            }

        } catch (Exception $e) {
            error_log('BookInn Create Room Type Error: ' . $e->getMessage());
            wp_send_json_error('Failed to create room type');
        }
    }

    /**
     * Update room type
     */
    public function update_room_type() {
        if (!$this->verify_nonce()) return;

        try {
            $room_type_id = intval($_POST['room_type_id'] ?? 0);
            
            if (!$room_type_id) {
                wp_send_json_error('Room type ID is required');
                return;
            }

            $room_type_data = array(
                'hotel_id' => intval($_POST['hotel_id'] ?? 1),
                'name' => sanitize_text_field($_POST['name'] ?? ''),
                'description' => sanitize_textarea_field($_POST['description'] ?? ''),
                'max_adults' => intval($_POST['max_adults'] ?? 2),
                'max_children' => intval($_POST['max_children'] ?? 0),
                'max_guests' => intval($_POST['max_guests'] ?? 2),
                'base_price' => floatval($_POST['base_price'] ?? 0),
                'amenities' => sanitize_textarea_field($_POST['amenities'] ?? ''),
                'images' => sanitize_textarea_field($_POST['images'] ?? ''),
                'is_active' => intval($_POST['is_active'] ?? 1),
                'updated_at' => current_time('mysql')
            );

            // Validation
            if (empty($room_type_data['name'])) {
                wp_send_json_error('Room type name is required');
                return;
            }

            global $wpdb;

            $result = $wpdb->update(
                $wpdb->prefix . 'bookinn_room_types',
                $room_type_data,
                array('id' => $room_type_id),
                array('%d', '%s', '%s', '%d', '%d', '%d', '%f', '%s', '%s', '%d', '%s'),
                array('%d')
            );

            if ($result !== false) {
                wp_send_json_success('Room type updated successfully');
            } else {
                wp_send_json_error('Failed to update room type');
            }

        } catch (Exception $e) {
            error_log('BookInn Update Room Type Error: ' . $e->getMessage());
            wp_send_json_error('Failed to update room type');
        }
    }

    /**
     * Delete room type
     */
    public function delete_room_type() {
        if (!$this->verify_nonce()) return;

        $room_type_id = intval($_POST['room_type_id'] ?? 0);

        if (!$room_type_id) {
            wp_send_json_error('Room type ID is required');
            return;
        }

        global $wpdb;

        try {
            $result = $wpdb->delete(
                $wpdb->prefix . 'bookinn_room_types',
                array('id' => $room_type_id),
                array('%d')
            );

            if ($result) {
                wp_send_json_success('Room type deleted successfully');
            } else {
                wp_send_json_error('Failed to delete room type');
            }

        } catch (Exception $e) {
            error_log('BookInn Delete Room Type Error: ' . $e->getMessage());
            wp_send_json_error('Failed to delete room type');
        }
    }

    /**
     * Get availability-based colors for calendar display
     */
    private function get_availability_color($status) {
        $colors = array(
            'confirmed' => array(
                'bg' => '#dc3545',      // Rosso - Fully booked
                'border' => '#c82333',
                'text' => '#ffffff'
            ),
            'pending' => array(
                'bg' => '#fd7e14',      // Arancione - Partial availability  
                'border' => '#e55e00',
                'text' => '#ffffff'
            ),
            'checked_in' => array(
                'bg' => '#dc3545',      // Rosso - Occupied
                'border' => '#c82333',
                'text' => '#ffffff'
            ),
            'checked_out' => array(
                'bg' => '#28a745',      // Verde - Available
                'border' => '#1e7e34',
                'text' => '#ffffff'
            ),
            'cancelled' => array(
                'bg' => '#28a745',      // Verde - Available (cancelled = room available)
                'border' => '#1e7e34',
                'text' => '#ffffff'
            )
        );
        
        return $colors[$status] ?? $colors['confirmed'];
    }

    /**
     * Format event title for calendar display
     */
    private function format_event_title($booking) {
        $guest_name = !empty($booking->guest_full_name) ? $booking->guest_full_name : 
                     (!empty($booking->guest_name) ? $booking->guest_name : 'Guest');
        $room_info = !empty($booking->room_number) ? $booking->room_number : 'Room';
        
        return $guest_name . ' - ' . $room_info;
    }

    /**
     * Get availability indicators for dates without bookings
     */
    private function get_availability_indicators($start, $end, $room_filter = 0) {
        // This method would analyze room availability for dates without bookings
        // and create "available" events to show green availability
        
        $availability_events = array();
        
        // For now, return empty array - this feature can be enhanced later
        // to show daily availability status for each room
        
        return $availability_events;
    }

    /**
     * Get room availability events for calendar
     */
    private function get_room_availability_events($start, $end, $room_filter = 0, $room_type_filter = 0) {
        global $wpdb;
        
        $availability_events = array();
        
        try {
            // Get all rooms (filtered if necessary)
            $room_where = array('1=1');
            $room_params = array();
            
            if ($room_filter > 0) {
                $room_where[] = 'r.id = %d';
                $room_params[] = $room_filter;
            }
            
            if ($room_type_filter > 0) {
                $room_where[] = 'r.room_type_id = %d';
                $room_params[] = $room_type_filter;
            }
            
            $room_query = "
                SELECT r.*, rt.name as room_type_name
                FROM {$wpdb->prefix}bookinn_rooms r
                LEFT JOIN {$wpdb->prefix}bookinn_room_types rt ON r.room_type_id = rt.id
                WHERE " . implode(' AND ', $room_where) . "
                ORDER BY r.room_number
            ";
            
            if (!empty($room_params)) {
                $rooms = $wpdb->get_results($wpdb->prepare($room_query, $room_params));
            } else {
                $rooms = $wpdb->get_results($room_query);
            }
            
            // Generate availability events for each day in the range
            $current_date = new DateTime($start);
            $end_date = new DateTime($end);
            
            while ($current_date <= $end_date) {
                $check_date = $current_date->format('Y-m-d');
                
                foreach ($rooms as $room) {
                    // Check if room is booked on this date
                    $booking_count = $wpdb->get_var($wpdb->prepare("
                        SELECT COUNT(*) 
                        FROM {$wpdb->prefix}bookinn_bookings 
                        WHERE room_id = %d 
                        AND check_in_date <= %s 
                        AND check_out_date > %s 
                        AND status IN ('confirmed', 'checked_in')
                    ", $room->id, $check_date, $check_date));
                    
                    if ($booking_count == 0) {
                        // Room is available - add green availability event
                        $availability_events[] = array(
                            'id' => 'availability_' . $room->id . '_' . $check_date,
                            'title' => '✅ ' . $room->room_number . ' Disponibile',
                            'start' => $check_date,
                            'end' => $check_date,
                            'allDay' => true,
                            'backgroundColor' => '#28a745',
                            'borderColor' => '#1e7e34',
                            'textColor' => '#ffffff',
                            'display' => 'background',
                            'extendedProps' => array(
                                'room_id' => $room->id,
                                'room_number' => $room->room_number,
                                'room_type' => $room->room_type_name,
                                'type' => 'availability',
                                'status' => 'available'
                            )
                        );
                    }
                }
                
                $current_date->add(new DateInterval('P1D'));
            }
            
        } catch (Exception $e) {
            error_log('BookInn Availability Events Error: ' . $e->getMessage());
        }
        
        return $availability_events;
    }

    /**
     * Get booking status color for calendar display (legacy method)
     */
    private function get_booking_color($status) {
        // Keep legacy method for backward compatibility
        $availability_color = $this->get_availability_color($status);
        return $availability_color['bg'];
    }

    /**
     * Unified save room handler - determines create vs update
     */
    public function save_room() {
        if (!$this->verify_nonce()) return;

        $room_id = intval($_POST['room_id'] ?? 0);
        
        if ($room_id > 0) {
            $this->update_room();
        } else {
            $this->create_room();
        }
    }

    /**
     * Unified save room type handler - determines create vs update
     */
    public function save_room_type() {
        if (!$this->verify_nonce()) return;

        $room_type_id = intval($_POST['room_type_id'] ?? 0);
        
        if ($room_type_id > 0) {
            $this->update_room_type();
        } else {
            $this->create_room_type();
        }
    }
}

// Initialize the unified AJAX handlers
new BookInn_Unified_Ajax_Handlers();
