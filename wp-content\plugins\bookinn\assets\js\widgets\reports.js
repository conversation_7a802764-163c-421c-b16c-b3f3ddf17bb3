/**
 * BookInn Reports Widget JavaScript
 * Handles analytics dashboard functionality, charts, and data visualization
 */

(function($) {
    'use strict';

    // Reports Widget Class
    class BookInnReportsWidget {
        constructor(container) {
            this.container = $(container);
            this.currentTab = null;
            this.charts = {};
            this.autoRefreshTimer = null;
            this.settings = window.bookinnReportsAjax?.settings || {};
            this.init();
        }

        init() {
            console.log('BookInn Reports Widget: Initializing...');
            console.log('Chart availability:', typeof Chart !== 'undefined' ? 'Available' : 'Not available');
            
            // Wait for Chart.js to be available before initializing
            this.waitForChart().then(() => {
                console.log('BookInn Reports Widget: Chart.js loaded successfully');
                this.bindEvents();
                this.initTabs();
                this.initDateFilters();
                this.startAutoRefresh();
                this.loadInitialData();
            }).catch((error) => {
                console.error('BookInn Reports Widget: Chart.js failed to load', error);
                // Initialize without charts but show warning
                this.showChartError();
                this.bindEvents();
                this.initTabs();
                this.initDateFilters();
                this.startAutoRefresh();
                this.loadInitialData();
            });
        }
        
        showChartError() {
            this.container.prepend('<div class="bookinn-notice bookinn-notice-warning"><p>Chart.js failed to load. Charts will not be displayed. Please check your internet connection.</p></div>');
        }

        waitForChart() {
            return new Promise((resolve, reject) => {
                if (typeof Chart !== 'undefined') {
                    resolve();
                    return;
                }

                let attempts = 0;
                const maxAttempts = 100; // 10 seconds total (100 * 100ms)
                
                const checkChart = () => {
                    if (typeof Chart !== 'undefined') {
                        resolve();
                        return;
                    }
                    
                    attempts++;
                    if (attempts >= maxAttempts) {
                        reject(new Error('Chart.js failed to load after ' + (maxAttempts * 100) + 'ms'));
                        return;
                    }
                    
                    setTimeout(checkChart, 100);
                };
                
                checkChart();
            });
        }

        bindEvents() {
            // Tab navigation
            this.container.on('click', '.bookinn-tab-link', (e) => {
                e.preventDefault();
                const tab = $(e.currentTarget).closest('.bookinn-tab-item').data('tab');
                this.switchTab(tab);
            });

            // Date filter changes
            this.container.on('change', '.bookinn-date-filter', (e) => {
                this.handleDateFilterChange(e);
            });

            // Custom date inputs
            this.container.on('change', '.bookinn-date-input', () => {
                this.refreshCurrentTab();
            });

            // Action buttons
            this.container.on('click', '[data-action="refresh-reports"]', () => {
                this.refreshCurrentTab();
            });

            this.container.on('click', '[data-action="export-reports"]', () => {
                this.exportCurrentReport();
            });

            // Export buttons
            this.container.on('click', '.bookinn-export-btn', (e) => {
                const format = $(e.currentTarget).data('format');
                this.exportReport(format);
            });
        }

        initTabs() {
            const defaultTab = this.settings.defaultView || 'overview';
            const urlHash = window.location.hash.substring(1);
            const activeTab = urlHash && this.isValidTab(urlHash) ? urlHash : defaultTab;
            
            this.switchTab(activeTab);
        }

        initDateFilters() {
            const dateFilter = this.container.find('.bookinn-date-filter');
            const customDates = this.container.find('.bookinn-custom-dates');

            // Show/hide custom date inputs
            dateFilter.on('change', function() {
                if ($(this).val() === 'custom') {
                    customDates.show();
                } else {
                    customDates.hide();
                }
            });

            // Set initial state
            if (dateFilter.val() === 'custom') {
                customDates.show();
            }
        }

        switchTab(tabName) {
            if (this.currentTab === tabName) return;

            // Update tab navigation
            this.container.find('.bookinn-tab-item').removeClass('active');
            this.container.find(`[data-tab="${tabName}"]`).addClass('active');

            // Update tab content
            this.container.find('.bookinn-tab-pane').removeClass('active');
            this.container.find(`#${tabName}`).addClass('active');

            // Update URL hash
            if (history.replaceState) {
                history.replaceState(null, null, `#${tabName}`);
            }

            this.currentTab = tabName;
            this.loadTabData(tabName);
        }

        isValidTab(tabName) {
            const validTabs = ['overview', 'revenue', 'occupancy', 'guests', 'performance'];
            return validTabs.includes(tabName);
        }

        loadTabData(tabName) {
            const contentContainer = this.container.find(`#${tabName} [class*="${tabName}-content"]`);
            
            if (!contentContainer.length) return;

            // Show loading state
            this.showTabLoading(contentContainer, tabName);

            // Get date range
            const dateRange = this.getDateRange();

            // Load data via AJAX
            this.makeAjaxRequest(`load_reports_${tabName}`, {
                date_range: dateRange.period,
                date_from: dateRange.from,
                date_to: dateRange.to,
                tab: tabName
            })
            .done((response) => {
                if (response.success && response.data.html) {
                    contentContainer.html(response.data.html);
                    this.initTabCharts(tabName, response.data.chartData || {});
                } else {
                    this.showTabError(contentContainer, response.data?.message || 'Failed to load data');
                }
            })
            .fail(() => {
                this.showTabError(contentContainer, 'Network error occurred');
            });
        }

        loadInitialData() {
            if (this.currentTab) {
                this.loadTabData(this.currentTab);
            }
        }

        showTabLoading(container, tabName) {
            const loadingMessage = this.getLoadingMessage(tabName);
            container.html(`
                <div class="bookinn-loading-spinner">
                    <span class="spinner"></span>
                    <span class="bookinn-loading-message">${loadingMessage}</span>
                </div>
            `);
        }

        showTabError(container, message) {
            container.html(`
                <div class="bookinn-error">
                    <h3>Error Loading Data</h3>
                    <p>${message}</p>
                    <button type="button" class="bookinn-btn bookinn-btn-secondary" onclick="location.reload()">
                        Retry
                    </button>
                </div>
            `);
        }

        getLoadingMessage(tabName) {
            const messages = {
                overview: 'Loading overview analytics...',
                revenue: 'Loading revenue data...',
                occupancy: 'Loading occupancy metrics...',
                guests: 'Loading guest analytics...',
                performance: 'Loading performance indicators...'
            };
            return messages[tabName] || 'Loading...';
        }

        getDateRange() {
            const period = this.container.find('.bookinn-date-filter').val();
            let from = null;
            let to = null;

            if (period === 'custom') {
                from = this.container.find('input[name="date_from"]').val();
                to = this.container.find('input[name="date_to"]').val();
            }

            return { period, from, to };
        }

        handleDateFilterChange(e) {
            const period = $(e.target).val();
            
            if (period === 'custom') {
                this.container.find('.bookinn-custom-dates').show();
            } else {
                this.container.find('.bookinn-custom-dates').hide();
                this.refreshCurrentTab();
            }
        }

        refreshCurrentTab() {
            if (this.currentTab) {
                this.loadTabData(this.currentTab);
            }
        }

        initTabCharts(tabName, chartData) {
            switch (tabName) {
                case 'overview':
                    this.initOverviewCharts(chartData);
                    break;
                case 'revenue':
                    this.initRevenueCharts(chartData);
                    break;
                case 'occupancy':
                    this.initOccupancyCharts(chartData);
                    break;
                case 'guests':
                    this.initGuestCharts(chartData);
                    break;
                case 'performance':
                    this.initPerformanceCharts(chartData);
                    break;
            }
        }

        initOverviewCharts(data) {
            // Revenue trend chart
            if (data.revenueTrend && this.container.find('#overview-revenue-chart').length) {
                this.createLineChart('overview-revenue-chart', {
                    labels: data.revenueTrend.labels,
                    datasets: [{
                        label: 'Revenue',
                        data: data.revenueTrend.data,
                        borderColor: '#274690',
                        backgroundColor: 'rgba(39, 70, 144, 0.1)',
                        fill: true
                    }]
                });
            }

            // Bookings trend chart
            if (data.bookingsTrend && this.container.find('#overview-bookings-chart').length) {
                this.createLineChart('overview-bookings-chart', {
                    labels: data.bookingsTrend.labels,
                    datasets: [{
                        label: 'Bookings',
                        data: data.bookingsTrend.data,
                        borderColor: '#10b981',
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        fill: true
                    }]
                });
            }
        }

        initRevenueCharts(data) {
            // Monthly revenue chart
            if (data.monthlyRevenue && this.container.find('#revenue-monthly-chart').length) {
                this.createBarChart('revenue-monthly-chart', {
                    labels: data.monthlyRevenue.labels,
                    datasets: [{
                        label: 'Monthly Revenue',
                        data: data.monthlyRevenue.data,
                        backgroundColor: '#274690'
                    }]
                });
            }

            // Revenue by source chart
            if (data.revenueBySource && this.container.find('#revenue-source-chart').length) {
                this.createDoughnutChart('revenue-source-chart', {
                    labels: data.revenueBySource.labels,
                    datasets: [{
                        data: data.revenueBySource.data,
                        backgroundColor: ['#274690', '#667eea', '#10b981', '#f59e0b', '#ef4444']
                    }]
                });
            }
        }

        initOccupancyCharts(data) {
            // Occupancy rate chart
            if (data.occupancyRate && this.container.find('#occupancy-rate-chart').length) {
                this.createLineChart('occupancy-rate-chart', {
                    labels: data.occupancyRate.labels,
                    datasets: [{
                        label: 'Occupancy Rate (%)',
                        data: data.occupancyRate.data,
                        borderColor: '#3b82f6',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        fill: true
                    }]
                });
            }

            // Room type occupancy
            if (data.roomTypeOccupancy && this.container.find('#room-type-occupancy-chart').length) {
                this.createBarChart('room-type-occupancy-chart', {
                    labels: data.roomTypeOccupancy.labels,
                    datasets: [{
                        label: 'Occupancy Rate (%)',
                        data: data.roomTypeOccupancy.data,
                        backgroundColor: ['#274690', '#667eea', '#10b981', '#f59e0b']
                    }]
                });
            }
        }

        initGuestCharts(data) {
            // Guest segments chart
            if (data.guestSegments && this.container.find('#guest-segments-chart').length) {
                this.createPieChart('guest-segments-chart', {
                    labels: data.guestSegments.labels,
                    datasets: [{
                        data: data.guestSegments.data,
                        backgroundColor: ['#274690', '#667eea', '#10b981', '#f59e0b', '#ef4444']
                    }]
                });
            }

            // Guest trends chart
            if (data.guestTrends && this.container.find('#guest-trends-chart').length) {
                this.createLineChart('guest-trends-chart', {
                    labels: data.guestTrends.labels,
                    datasets: [{
                        label: 'New Guests',
                        data: data.guestTrends.new,
                        borderColor: '#10b981',
                        backgroundColor: 'rgba(16, 185, 129, 0.1)'
                    }, {
                        label: 'Returning Guests',
                        data: data.guestTrends.returning,
                        borderColor: '#274690',
                        backgroundColor: 'rgba(39, 70, 144, 0.1)'
                    }]
                });
            }
        }

        initPerformanceCharts(data) {
            // ADR trend chart
            if (data.adrTrend && this.container.find('#adr-trend-chart').length) {
                this.createLineChart('adr-trend-chart', {
                    labels: data.adrTrend.labels,
                    datasets: [{
                        label: 'Average Daily Rate',
                        data: data.adrTrend.data,
                        borderColor: '#f59e0b',
                        backgroundColor: 'rgba(245, 158, 11, 0.1)',
                        fill: true
                    }]
                });
            }

            // RevPAR trend chart
            if (data.revparTrend && this.container.find('#revpar-trend-chart').length) {
                this.createLineChart('revpar-trend-chart', {
                    labels: data.revparTrend.labels,
                    datasets: [{
                        label: 'Revenue Per Available Room',
                        data: data.revparTrend.data,
                        borderColor: '#8b5cf6',
                        backgroundColor: 'rgba(139, 92, 246, 0.1)',
                        fill: true
                    }]
                });
            }
        }

        createLineChart(canvasId, data, options = {}) {
            const ctx = document.getElementById(canvasId);
            if (!ctx) return;
            
            // Check if Chart.js is loaded
            if (typeof Chart === 'undefined') {
                console.error('Chart.js is not loaded. Make sure it is included before this script.');
                return;
            }

            // Destroy existing chart
            if (this.charts[canvasId]) {
                this.charts[canvasId].destroy();
            }

            const defaultOptions = {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            };

            this.charts[canvasId] = new Chart(ctx, {
                type: 'line',
                data: data,
                options: { ...defaultOptions, ...options }
            });
        }

        createBarChart(canvasId, data, options = {}) {
            const ctx = document.getElementById(canvasId);
            if (!ctx) return;
            
            // Check if Chart.js is loaded
            if (typeof Chart === 'undefined') {
                console.error('Chart.js is not loaded. Make sure it is included before this script.');
                return;
            }

            // Destroy existing chart
            if (this.charts[canvasId]) {
                this.charts[canvasId].destroy();
            }

            const defaultOptions = {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            };

            this.charts[canvasId] = new Chart(ctx, {
                type: 'bar',
                data: data,
                options: { ...defaultOptions, ...options }
            });
        }

        createDoughnutChart(canvasId, data, options = {}) {
            const ctx = document.getElementById(canvasId);
            if (!ctx) return;
            
            // Check if Chart.js is loaded
            if (typeof Chart === 'undefined') {
                console.error('Chart.js is not loaded. Make sure it is included before this script.');
                return;
            }

            // Destroy existing chart
            if (this.charts[canvasId]) {
                this.charts[canvasId].destroy();
            }

            const defaultOptions = {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            };

            this.charts[canvasId] = new Chart(ctx, {
                type: 'doughnut',
                data: data,
                options: { ...defaultOptions, ...options }
            });
        }

        createPieChart(canvasId, data, options = {}) {
            const ctx = document.getElementById(canvasId);
            if (!ctx) return;
            
            // Check if Chart.js is loaded
            if (typeof Chart === 'undefined') {
                console.error('Chart.js is not loaded. Make sure it is included before this script.');
                return;
            }

            // Destroy existing chart
            if (this.charts[canvasId]) {
                this.charts[canvasId].destroy();
            }

            const defaultOptions = {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            };

            this.charts[canvasId] = new Chart(ctx, {
                type: 'pie',
                data: data,
                options: { ...defaultOptions, ...options }
            });
        }

        exportCurrentReport() {
            this.exportReport('pdf');
        }

        exportReport(format = 'pdf') {
            const dateRange = this.getDateRange();
            
            // Create form and submit
            const form = $('<form>', {
                method: 'POST',
                action: window.bookinnReportsAjax?.url || ajaxurl
            });

            const fields = {
                action: 'bookinn_export_report',
                nonce: window.bookinnReportsAjax?.nonce || '',
                tab: this.currentTab,
                format: format,
                date_range: dateRange.period,
                date_from: dateRange.from,
                date_to: dateRange.to
            };

            $.each(fields, (name, value) => {
                form.append($('<input>', {
                    type: 'hidden',
                    name: name,
                    value: value
                }));
            });

            $('body').append(form);
            form.submit();
            form.remove();
        }

        startAutoRefresh() {
            if (this.settings.autoRefresh > 0) {
                this.autoRefreshTimer = setInterval(() => {
                    this.refreshCurrentTab();
                }, this.settings.autoRefresh * 1000);
            }
        }

        stopAutoRefresh() {
            if (this.autoRefreshTimer) {
                clearInterval(this.autoRefreshTimer);
                this.autoRefreshTimer = null;
            }
        }

        makeAjaxRequest(action, data = {}) {
            // Use consistent AJAX configuration across all widgets
            const ajaxUrl = window.bookinnReportsAjax?.ajaxurl ||
                           window.bookinnReportsAjax?.url ||
                           window.bookinnAjax?.url ||
                           window.bookinn_dashboard?.ajax_url ||
                           '/wp-admin/admin-ajax.php';

            const nonce = window.bookinnReportsAjax?.nonce ||
                         window.bookinnAjax?.nonce ||
                         window.bookinn_dashboard?.nonce ||
                         '';

            const requestData = {
                action: `bookinn_${action}`,
                nonce: nonce,
                ...data
            };

            console.log('Reports AJAX request:', {
                action: `bookinn_${action}`,
                url: ajaxUrl,
                nonce: nonce ? 'present' : 'missing',
                data: requestData
            });

            return $.ajax({
                url: ajaxUrl,
                type: 'POST',
                data: requestData,
                dataType: 'json'
            }).fail((xhr, status, error) => {
                console.error('Reports AJAX request failed:', {
                    action: `bookinn_${action}`,
                    status: xhr.status,
                    statusText: xhr.statusText,
                    responseText: xhr.responseText,
                    error: error
                });
            });
        }

        destroy() {
            this.stopAutoRefresh();
            
            // Destroy all charts
            Object.values(this.charts).forEach(chart => {
                if (chart && typeof chart.destroy === 'function') {
                    chart.destroy();
                }
            });
            
            this.container.off();
        }
    }

    // Initialize reports widgets when document is ready
    $(document).ready(function() {
        $('.bookinn-reports-interface').each(function() {
            new BookInnReportsWidget(this);
        });
    });

    // Expose for global access
    window.BookInnReportsWidget = BookInnReportsWidget;

})(jQuery);
