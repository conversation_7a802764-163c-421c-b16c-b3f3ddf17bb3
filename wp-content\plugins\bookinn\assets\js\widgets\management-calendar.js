/**
 * BookInn Management Widget - Interactive Calendar
 * 
 * Handles calendar functionality with FullCalendar integration
 */

(function($) {
    'use strict';
    
    // Calendar Object
    window.BookInn = window.BookInn || {};
    window.BookInn.ManagementCalendar = {
        
        calendar: null,
        
        /**
         * Initialize calendar
         */
        init: function() {
            console.log('BookInn Management Calendar: Initializing...');
            
            if (typeof FullCalendar === 'undefined') {
                console.warn('FullCalendar not available, using fallback');
                this.initFallbackCalendar();
                return;
            }
            
            this.initFullCalendar();
            this.bindEvents();
        },
        
        /**
         * Initialize FullCalendar
         */
        initFullCalendar: function() {
            const calendarEl = document.getElementById('bookinn-calendar');
            
            if (!calendarEl) {
                console.warn('Calendar element not found');
                return;
            }
            
            this.calendar = new FullCalendar.Calendar(calendarEl, {
                initialView: 'dayGridMonth',
                headerToolbar: {
                    left: 'prev,next today',
                    center: 'title',
                    right: 'dayGridMonth,timeGridWeek,listWeek'
                },
                height: 'auto',
                events: this.loadEvents.bind(this),
                eventClick: this.handleEventClick.bind(this),
                dateClick: this.handleDateClick.bind(this),
                eventDrop: this.handleEventDrop.bind(this),
                eventResize: this.handleEventResize.bind(this),
                loading: this.handleLoading.bind(this),
                eventDidMount: this.handleEventMount.bind(this),
                dayMaxEvents: 3,
                moreLinkClick: 'popover',
                editable: true,
                droppable: false,
                selectable: true,
                selectMirror: true,
                weekends: true,
                nowIndicator: true,
                eventDisplay: 'block',
                displayEventTime: false,
                eventTextColor: '#fff',
                eventBorderColor: 'transparent'
            });
            
            this.calendar.render();
            console.log('FullCalendar initialized successfully');
        },
        
        /**
         * Load events from server
         */
        loadEvents: function(info, successCallback, failureCallback) {
            console.log('Loading calendar events...', info.startStr, 'to', info.endStr);
            
            $.ajax({
                url: bookinnAjax.url,
                type: 'POST',
                data: {
                    action: 'bookinn_get_calendar_events',
                    nonce: bookinnAjax.nonce,
                    start: info.startStr,
                    end: info.endStr
                },
                success: function(response) {
                    if (response.success) {
                        console.log('Calendar events loaded:', response.data.length);
                        successCallback(response.data);
                    } else {
                        console.error('Error loading calendar events:', response.message);
                        failureCallback();
                    }
                },
                error: function() {
                    console.error('AJAX error loading calendar events');
                    failureCallback();
                }
            });
        },
        
        /**
         * Handle event click
         */
        handleEventClick: function(info) {
            console.log('Event clicked:', info.event);
            
            const booking = info.event.extendedProps;
            
            const modalHtml = `
                <div class="bookinn-modal-overlay" id="booking-details-modal">
                    <div class="bookinn-modal">
                        <div class="bookinn-modal-header">
                            <h3>Booking Details - #${booking.booking_id}</h3>
                            <button type="button" class="bookinn-modal-close">&times;</button>
                        </div>
                        <div class="bookinn-modal-body">
                            <div class="bookinn-booking-details-grid">
                                <div class="bookinn-detail-item">
                                    <label>Guest:</label>
                                    <span>${booking.guest_name}</span>
                                </div>
                                <div class="bookinn-detail-item">
                                    <label>Room:</label>
                                    <span>${booking.room_number}</span>
                                </div>
                                <div class="bookinn-detail-item">
                                    <label>Check-in:</label>
                                    <span>${info.event.startStr}</span>
                                </div>
                                <div class="bookinn-detail-item">
                                    <label>Check-out:</label>
                                    <span>${info.event.endStr}</span>
                                </div>
                                <div class="bookinn-detail-item">
                                    <label>Status:</label>
                                    <span class="bookinn-status-badge ${booking.status}">${booking.status.replace('_', ' ').toUpperCase()}</span>
                                </div>
                                <div class="bookinn-detail-item">
                                    <label>Total Amount:</label>
                                    <span>€${booking.total_amount}</span>
                                </div>
                            </div>
                        </div>
                        <div class="bookinn-modal-footer">
                            <button type="button" class="bookinn-btn bookinn-btn-secondary" data-action="close-modal">Close</button>
                            <button type="button" class="bookinn-btn bookinn-btn-primary" data-action="edit-booking" data-booking-id="${booking.booking_id}">Edit Booking</button>
                        </div>
                    </div>
                </div>
            `;
            
            $('#bookinn-modal-container').html(modalHtml);
            this.bindModalEvents();
        },
        
        /**
         * Handle date click (create new booking)
         */
        handleDateClick: function(info) {
            console.log('Date clicked:', info.dateStr);
            
            // Pre-fill new booking modal with selected date
            if (window.BookInn && window.BookInn.ManagementActions) {
                window.BookInn.ManagementActions.showNewBookingModal();
                
                // Wait for modal to be created, then set date
                setTimeout(function() {
                    $('[name="check_in_date"]').val(info.dateStr);
                    
                    // Set check-out to next day
                    const nextDay = new Date(info.date);
                    nextDay.setDate(nextDay.getDate() + 1);
                    const nextDayStr = nextDay.toISOString().split('T')[0];
                    $('[name="check_out_date"]').val(nextDayStr);
                }, 100);
            }
        },
        
        /**
         * Handle event drop (reschedule booking)
         */
        handleEventDrop: function(info) {
            console.log('Event dropped:', info.event);
            
            const bookingId = info.event.extendedProps.booking_id;
            const newStart = info.event.startStr;
            const newEnd = info.event.endStr;
            
            if (confirm('Reschedule this booking?')) {
                $.ajax({
                    url: bookinnAjax.url,
                    type: 'POST',
                    data: {
                        action: 'bookinn_update_booking',
                        nonce: bookinnAjax.nonce,
                        booking_id: bookingId,
                        check_in_date: newStart,
                        check_out_date: newEnd
                    },
                    success: function(response) {
                        if (response.success) {
                            console.log('Booking rescheduled successfully');
                        } else {
                            alert('Error rescheduling booking: ' + response.message);
                            info.revert();
                        }
                    },
                    error: function() {
                        alert('Error rescheduling booking');
                        info.revert();
                    }
                });
            } else {
                info.revert();
            }
        },
        
        /**
         * Handle event resize (change duration)
         */
        handleEventResize: function(info) {
            console.log('Event resized:', info.event);
            
            const bookingId = info.event.extendedProps.booking_id;
            const newEnd = info.event.endStr;
            
            if (confirm('Change booking duration?')) {
                $.ajax({
                    url: bookinnAjax.url,
                    type: 'POST',
                    data: {
                        action: 'bookinn_update_booking',
                        nonce: bookinnAjax.nonce,
                        booking_id: bookingId,
                        check_out_date: newEnd
                    },
                    success: function(response) {
                        if (response.success) {
                            console.log('Booking duration changed successfully');
                        } else {
                            alert('Error changing booking duration: ' + response.message);
                            info.revert();
                        }
                    },
                    error: function() {
                        alert('Error changing booking duration');
                        info.revert();
                    }
                });
            } else {
                info.revert();
            }
        },
        
        /**
         * Handle loading state
         */
        handleLoading: function(isLoading) {
            if (isLoading) {
                $('.bookinn-calendar-container').addClass('loading');
            } else {
                $('.bookinn-calendar-container').removeClass('loading');
            }
        },
        
        /**
         * Handle event mount (customize appearance)
         */
        handleEventMount: function(info) {
            // Add tooltip
            $(info.el).attr('title', `${info.event.extendedProps.guest_name} - ${info.event.extendedProps.room_number}`);
        },
        
        /**
         * Bind modal events
         */
        bindModalEvents: function() {
            const self = this;
            
            // Close modal
            $(document).on('click', '.bookinn-modal-close, [data-action="close-modal"]', function() {
                $('#bookinn-modal-container').empty();
            });
            
            // Close on overlay click
            $(document).on('click', '.bookinn-modal-overlay', function(e) {
                if (e.target === this) {
                    $('#bookinn-modal-container').empty();
                }
            });
            
            // Edit booking from calendar
            $(document).on('click', '[data-action="edit-booking"]', function() {
                const bookingId = $(this).data('booking-id');
                $('#bookinn-modal-container').empty();
                
                if (window.BookInn && window.BookInn.ManagementActions) {
                    window.BookInn.ManagementActions.editBooking(bookingId);
                }
            });
        },
        
        /**
         * Bind calendar events
         */
        bindEvents: function() {
            const self = this;
            
            // Calendar navigation buttons
            $(document).on('click', '[data-action="calendar-prev"]', function() {
                if (self.calendar) {
                    self.calendar.prev();
                }
            });
            
            $(document).on('click', '[data-action="calendar-next"]', function() {
                if (self.calendar) {
                    self.calendar.next();
                }
            });
            
            $(document).on('click', '[data-action="calendar-today"]', function() {
                if (self.calendar) {
                    self.calendar.today();
                }
            });
            
            // Refresh calendar when tab is switched
            $(document).on('bookinn:tab:changed', function(e, tabId) {
                if (tabId === 'calendar' && self.calendar) {
                    setTimeout(function() {
                        self.calendar.updateSize();
                        self.calendar.refetchEvents();
                    }, 100);
                }
            });
        },
        
        /**
         * Fallback calendar for when FullCalendar is not available
         */
        initFallbackCalendar: function() {
            const calendarEl = document.getElementById('bookinn-calendar');
            
            if (!calendarEl) {
                return;
            }
            
            calendarEl.innerHTML = `
                <div class="bookinn-calendar-fallback">
                    <h3>📅 Calendar View</h3>
                    <p>Interactive calendar functionality requires FullCalendar library.</p>
                    <p>Features available when FullCalendar is loaded:</p>
                    <ul>
                        <li>✅ View all bookings by date</li>
                        <li>✅ Drag & drop to reschedule</li>
                        <li>✅ Click dates to create bookings</li>
                        <li>✅ Click events to view details</li>
                        <li>✅ Resize events to change duration</li>
                    </ul>
                    <button type="button" class="bookinn-btn bookinn-btn-primary" onclick="location.reload()">
                        Reload Page
                    </button>
                </div>
            `;
        },
        
        /**
         * Refresh calendar
         */
        refresh: function() {
            if (this.calendar) {
                this.calendar.refetchEvents();
            }
        }
    };
    
    // Initialize when document is ready and calendar tab is active
    $(document).ready(function() {
        // Initialize when calendar tab becomes active
        $(document).on('bookinn:tab:changed', function(e, tabId) {
            if (tabId === 'calendar') {
                setTimeout(function() {
                    if (!BookInn.ManagementCalendar.calendar) {
                        BookInn.ManagementCalendar.init();
                    }
                }, 100);
            }
        });
        
        // Initialize immediately if calendar tab is already active
        if ($('#calendar').hasClass('is-active')) {
            setTimeout(function() {
                BookInn.ManagementCalendar.init();
            }, 1000);
        }
    });
    
})(jQuery);
